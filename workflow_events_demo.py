#!/usr/bin/env python3
"""
Workflow Events Demo

Demonstrates the new event system in the workflow engine.
Shows how to listen to step completion events and workflow progress.

Author: Assistant
"""

import asyncio
import time
from typing import Dict, Any
from workflow.engine import WorkflowEngine
from workflow.models import WorkflowEvent, WorkflowConfig, WorkflowStep, WorkflowSettings


def print_header(title: str):
    """Print a formatted header"""
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")


def print_step(step_num: int, title: str):
    """Print a formatted step header"""
    print(f"\n🔸 Step {step_num}: {title}")
    print("-" * 40)


# Event callback functions
def step_completed_callback(event: WorkflowEvent):
    """Synchronous callback for step completion events"""
    if event.event_type == "step_completed":
        step_result = event.step_result
        progress = event.workflow_progress
        
        status_emoji = {
            "success": "✅",
            "error": "❌", 
            "skipped": "⏭️"
        }.get(step_result.status, "❓")
        
        print(f"  {status_emoji} Step '{event.step_name}' completed: {step_result.status}")
        print(f"     Execution time: {step_result.execution_time}s")
        print(f"     Progress: {progress['completed_steps']}/{progress['total_steps']} ({progress['progress_percentage']}%)")
        if step_result.error:
            print(f"     Error: {step_result.error}")


async def workflow_progress_callback(event: WorkflowEvent):
    """Asynchronous callback for all workflow events"""
    if event.event_type == "workflow_started":
        print(f"🚀 Workflow '{event.workflow_name}' started")
        print(f"   Total steps: {event.workflow_progress['total_steps']}")
    
    elif event.event_type == "workflow_completed":
        print(f"🏁 Workflow '{event.workflow_name}' completed")
        print(f"   Status: {event.metadata['overall_status']}")
        print(f"   Total time: {event.metadata['total_execution_time']}s")
        print(f"   Summary: {event.metadata['summary']}")


def detailed_event_logger(event: WorkflowEvent):
    """Detailed event logger for debugging"""
    timestamp = event.timestamp.strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] EVENT: {event.event_type}")
    
    if event.step_name:
        print(f"           Step: {event.step_name}")
    
    if event.step_result:
        print(f"           Result: {event.step_result.status} ({event.step_result.execution_time}s)")
    
    if event.metadata:
        print(f"           Metadata: {event.metadata}")


async def demonstrate_workflow_events():
    """Demonstrate the workflow event system"""
    
    print_header("WORKFLOW EVENT SYSTEM DEMONSTRATION")
    
    print_step(1, "Setting up Event Callbacks")
    
    # Create workflow engine with event callbacks
    engine = WorkflowEngine(event_callbacks=[
        step_completed_callback,           # Sync callback for step completion
        workflow_progress_callback,        # Async callback for workflow events
        detailed_event_logger             # Detailed logging callback
    ])
    
    print("✅ Workflow engine created with 3 event callbacks:")
    print("   • step_completed_callback (sync) - Shows step completion status")
    print("   • workflow_progress_callback (async) - Shows workflow start/end")
    print("   • detailed_event_logger (sync) - Detailed event logging")
    
    print_step(2, "Creating Sample Workflow Configuration")
    
    # Create a simple workflow configuration
    config = WorkflowConfig(
        name="Event Demo Workflow",
        description="A simple workflow to demonstrate event firing",
        steps=[
            WorkflowStep(
                name="step_1",
                tool_name="file.read",
                input_data={"path": "README.md"}
            ),
            WorkflowStep(
                name="step_2", 
                tool_name="file.list",
                input_data={"path": "."},
                depends_on=["step_1"]
            ),
            WorkflowStep(
                name="step_3",
                tool_name="file.info",
                input_data={"path": "workflow"},
                depends_on=["step_1"]
            )
        ],
        settings=WorkflowSettings(
            max_concurrency=2,
            timeout=60,
            fail_fast=False
        )
    )
    
    print(f"✅ Created workflow '{config.name}' with {len(config.steps)} steps")
    print("   Steps: step_1 → [step_2, step_3] (parallel)")
    
    print_step(3, "Executing Workflow with Event Monitoring")
    print("Watch for events as the workflow executes...\n")
    
    # Execute the workflow
    start_time = time.time()
    try:
        result = await engine.execute(config)
        execution_time = time.time() - start_time
        
        print_step(4, "Execution Summary")
        print(f"✅ Workflow execution completed in {execution_time:.3f}s")
        print(f"📊 Results:")
        print(f"   • Total steps: {result.total_steps}")
        print(f"   • Successful: {result.successful_steps}")
        print(f"   • Failed: {result.failed_steps}")
        print(f"   • Status: {result.status}")
        
    except Exception as e:
        print(f"❌ Workflow execution failed: {e}")


async def demonstrate_parallel_events():
    """Demonstrate events with parallel execution"""
    
    print_header("PARALLEL EXECUTION EVENTS")
    
    print_step(1, "Creating Parallel Workflow")
    
    # Create engine with simple callback
    def simple_callback(event: WorkflowEvent):
        if event.event_type == "step_completed":
            parallel_info = " (parallel)" if event.metadata.get("parallel_execution") else ""
            print(f"   📝 {event.step_name} → {event.step_result.status}{parallel_info}")
    
    engine = WorkflowEngine(event_callbacks=[simple_callback])
    
    # Create workflow with parallel steps
    config = WorkflowConfig(
        name="Parallel Demo",
        steps=[
            WorkflowStep(name="init", tool_name="file.info", input_data={"path": "."}),
            WorkflowStep(name="task_a", tool_name="file.list", input_data={"path": "."}, depends_on=["init"]),
            WorkflowStep(name="task_b", tool_name="file.info", input_data={"path": "workflow"}, depends_on=["init"]),
            WorkflowStep(name="task_c", tool_name="file.info", input_data={"path": "tools"}, depends_on=["init"]),
            WorkflowStep(name="final", tool_name="file.info", input_data={"path": "README.md"}, depends_on=["task_a", "task_b", "task_c"])
        ],
        settings=WorkflowSettings(max_concurrency=3)
    )
    
    print("✅ Created parallel workflow: init → [task_a, task_b, task_c] → final")
    print("   Max concurrency: 3")
    print("\nExecuting with event monitoring:")
    
    result = await engine.execute(config)
    
    print(f"\n✅ Parallel workflow completed: {result.status}")


def demonstrate_event_types():
    """Show all available event types"""
    
    print_header("AVAILABLE EVENT TYPES")
    
    event_types = [
        ("workflow_started", "Fired when workflow execution begins"),
        ("step_completed", "Fired after each step completes (success, error, or skipped)"),
        ("workflow_completed", "Fired when entire workflow finishes")
    ]
    
    for event_type, description in event_types:
        print(f"🔸 {event_type}")
        print(f"   {description}")
    
    print("\n📋 Event Data Available:")
    print("   • event_type: Type of event")
    print("   • timestamp: When the event occurred")
    print("   • workflow_name: Name of the workflow")
    print("   • step_name: Name of the step (for step events)")
    print("   • step_result: Complete step result (for step_completed)")
    print("   • workflow_progress: Current progress information")
    print("   • metadata: Additional context-specific data")


async def main():
    """Main demonstration function"""
    
    # Show event types
    demonstrate_event_types()
    
    # Demonstrate basic workflow events
    await demonstrate_workflow_events()
    
    # Demonstrate parallel execution events
    await demonstrate_parallel_events()
    
    print_header("EVENT SYSTEM DEMONSTRATION COMPLETE")
    print("🎉 The workflow event system allows you to:")
    print("   • Monitor workflow progress in real-time")
    print("   • React to step completions immediately")
    print("   • Build custom monitoring and logging systems")
    print("   • Integrate with external systems (notifications, dashboards, etc.)")
    print("   • Handle both synchronous and asynchronous callbacks")


if __name__ == "__main__":
    asyncio.run(main())
