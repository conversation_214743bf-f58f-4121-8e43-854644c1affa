import asyncio
import subprocess
import os
import shlex
from typing import Optional, Dict, Any
from contextlib import AsyncExitStack

import click
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

from dotenv import load_dotenv

load_dotenv()  # load environment variables from .env


class MCPClient:
    def __init__(self):
        # Initialize session and client objects
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()

    # methods will go here

    async def connect_to_server(self, server_script_path: str):
        """Connect to an MCP server

        Args:
            server_script_path: Path to the server script (.py or .js)
        """
        is_python = server_script_path.endswith('.py')
        is_js = server_script_path.endswith('.js')
        if not (is_python or is_js):
            raise ValueError("Server script must be a .py or .js file")

        command = "python" if is_python else "node"
        server_params = StdioServerParameters(
            command=command,
            args=[server_script_path],
            env=None
        )

        stdio_transport = await self.exit_stack.enter_async_context(stdio_client(server_params))
        self.stdio, self.write = stdio_transport
        self.session = await self.exit_stack.enter_async_context(ClientSession(self.stdio, self.write))

        await self.session.initialize()

        # List available tools
        response = await self.session.list_tools()
        tools = response.tools
        click.echo(f"\nConnected to server with tools: {[tool.name for tool in tools]}")

    async def process_query(self, query: str) -> str:
        """Process a query by logging available tools and the query"""
        click.echo(f"\nProcessing query: {query}")

        response = await self.session.list_tools()
        available_tools = [{
            "name": tool.name,
            "description": tool.description,
            "input_schema": tool.inputSchema
        } for tool in response.tools]

        click.echo(f"Available tools: {available_tools}")
        await self.run_script()

        return f"Query processed: {query}"

    async def chat_loop(self):
        """Process a single query"""
        response = await self.process_query("test query")
        print("\n" + response)

    async def cleanup(self):
        """Clean up resources"""
        await self.exit_stack.aclose()

    async def run_script(self):
        try:

            result = await self.session.call_tool("github_tool.clone_template", {
                "template_url": "**************:Josephmaclean/complaints.git",
                "destination_path": "./complaints"
            })
            return result.content
        except Exception as e:
            click.echo(f"Error calling get_alerts: {str(e)}", err=True)
            return None


@click.group()
@click.version_option(version="1.0.0")
def cli():
    """MCP (Model Context Protocol) Client

    A client for connecting to and interacting with MCP servers.
    """
    pass


@cli.command()
@click.argument('server_script', type=click.Path(exists=True), required=True)
@click.option('--query', '-q', default="test query", help='Query to process (default: "test query")')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose output')
def connect(server_script, query, verbose):
    """Connect to an MCP server and process a query.

    SERVER_SCRIPT: Path to the MCP server script (.py or .js file)
    """
    async def run_client():
        client = MCPClient()
        try:
            if verbose:
                click.echo(f"Connecting to server: {server_script}")

            await client.connect_to_server(server_script)

            if verbose:
                click.echo(f"Processing query: {query}")

            response = await client.process_query(query)
            click.echo(f"\n{response}")

        except Exception as e:
            click.echo(f"Error: {str(e)}", err=True)
            raise click.ClickException(str(e))
        finally:
            await client.cleanup()

    asyncio.run(run_client())


@cli.command()
@click.argument('server_script', type=click.Path(exists=True), required=True)
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose output')
def list_tools(server_script, verbose):
    """List available tools from an MCP server.

    SERVER_SCRIPT: Path to the MCP server script (.py or .js file)
    """
    async def run_list_tools():
        client = MCPClient()
        try:
            if verbose:
                click.echo(f"Connecting to server: {server_script}")

            await client.connect_to_server(server_script)

            response = await client.session.list_tools()
            tools = response.tools

            click.echo("\nAvailable tools:")
            for tool in tools:
                click.echo(f"  • {tool.name}: {tool.description}")
                if verbose and tool.inputSchema:
                    click.echo(f"    Input schema: {tool.inputSchema}")

        except Exception as e:
            click.echo(f"Error: {str(e)}", err=True)
            raise click.ClickException(str(e))
        finally:
            await client.cleanup()

    asyncio.run(run_list_tools())


if __name__ == "__main__":
    cli()