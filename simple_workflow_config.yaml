# Simple workflow configuration that should work reliably
# This demonstrates a successful multi-repository cloning workflow

repositories:
  - name: "copier-templates-extensions"
    url: "https://github.com/copier-org/copier-templates-extensions.git"
    destination: "./cloned-repos/templates-extensions"
    type: "copier"
    data:
      project_name: "Templates Extensions Demo"
      author_name: "Workflow Demo User"
      description: "Cloned via workflow automation"
    
  - name: "simple-template"
    url: "https://github.com/copier-org/copier-templates-extensions.git"
    destination: "./cloned-repos/simple-template"
    type: "copier"
    # No checkout specified - will use default branch
    data:
      project_name: "Simple Template Demo"
      author_name: "Workflow Demo User"
      description: "Another cloned template"

# Workflow settings
workflow:
  max_concurrency: 2
  timeout: 180
  fail_fast: false
  cleanup_on_error: false
  
# Output settings
output:
  summary_file: "./simple_workflow_summary.json"
