"""
Workflow Configuration Readers

Pluggable readers for different configuration file formats.
Currently supports YAML, designed for easy extension to other formats.

Author: Assistant
"""

import abc
from pathlib import Path
from typing import Dict, Any, Type
from .models import WorkflowConfig


class ConfigReader(abc.ABC):
    """Abstract base class for configuration readers"""
    
    @abc.abstractmethod
    def can_read(self, file_path: str) -> bool:
        """Check if this reader can handle the given file"""
        pass
    
    @abc.abstractmethod
    def read(self, file_path: str) -> WorkflowConfig:
        """Read and parse the configuration file"""
        pass
    
    @property
    @abc.abstractmethod
    def supported_extensions(self) -> list:
        """List of supported file extensions"""
        pass


class YAMLReader(ConfigReader):
    """Reader for YAML configuration files"""
    
    def can_read(self, file_path: str) -> bool:
        """Check if file has YAML extension"""
        path = Path(file_path)
        return path.suffix.lower() in self.supported_extensions
    
    def read(self, file_path: str) -> WorkflowConfig:
        """Read YAML configuration file"""
        try:
            import yaml
        except ImportError:
            raise ImportError("PyYAM<PERSON> is required to read YAML configuration files")
        
        path = Path(file_path)
        if not path.exists():
            raise FileNotFoundError(f"Configuration file not found: {file_path}")
        
        try:
            with open(path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            
            if not data:
                raise ValueError("Configuration file is empty or invalid")
            
            # Convert to WorkflowConfig
            return WorkflowConfig(**data)
            
        except yaml.YAMLError as e:
            raise ValueError(f"Invalid YAML syntax: {e}")
        except Exception as e:
            raise ValueError(f"Failed to parse configuration: {e}")
    
    @property
    def supported_extensions(self) -> list:
        return ['.yaml', '.yml']


class JSONReader(ConfigReader):
    """Reader for JSON configuration files (future implementation)"""
    
    def can_read(self, file_path: str) -> bool:
        """Check if file has JSON extension"""
        path = Path(file_path)
        return path.suffix.lower() in self.supported_extensions
    
    def read(self, file_path: str) -> WorkflowConfig:
        """Read JSON configuration file"""
        import json
        
        path = Path(file_path)
        if not path.exists():
            raise FileNotFoundError(f"Configuration file not found: {file_path}")
        
        try:
            with open(path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return WorkflowConfig(**data)
            
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON syntax: {e}")
        except Exception as e:
            raise ValueError(f"Failed to parse configuration: {e}")
    
    @property
    def supported_extensions(self) -> list:
        return ['.json']


class TOMLReader(ConfigReader):
    """Reader for TOML configuration files (future implementation)"""
    
    def can_read(self, file_path: str) -> bool:
        """Check if file has TOML extension"""
        path = Path(file_path)
        return path.suffix.lower() in self.supported_extensions
    
    def read(self, file_path: str) -> WorkflowConfig:
        """Read TOML configuration file"""
        try:
            import tomli
        except ImportError:
            raise ImportError("tomli is required to read TOML configuration files")
        
        path = Path(file_path)
        if not path.exists():
            raise FileNotFoundError(f"Configuration file not found: {file_path}")
        
        try:
            with open(path, 'rb') as f:
                data = tomli.load(f)
            
            return WorkflowConfig(**data)
            
        except Exception as e:
            raise ValueError(f"Failed to parse TOML configuration: {e}")
    
    @property
    def supported_extensions(self) -> list:
        return ['.toml']


# Registry of available readers
_READERS: Dict[str, Type[ConfigReader]] = {
    'yaml': YAMLReader,
    'json': JSONReader,
    'toml': TOMLReader,
}


def get_reader(file_path: str) -> ConfigReader:
    """Get appropriate reader for the given file"""
    path = Path(file_path)
    
    # Try each reader to see which one can handle the file
    for reader_class in _READERS.values():
        reader = reader_class()
        if reader.can_read(file_path):
            return reader
    
    # If no reader found, provide helpful error message
    supported_extensions = []
    for reader_class in _READERS.values():
        reader = reader_class()
        supported_extensions.extend(reader.supported_extensions)
    
    raise ValueError(
        f"No reader available for file: {file_path}\n"
        f"Supported extensions: {', '.join(sorted(set(supported_extensions)))}"
    )


def register_reader(name: str, reader_class: Type[ConfigReader]) -> None:
    """Register a new configuration reader"""
    if not issubclass(reader_class, ConfigReader):
        raise TypeError("Reader must inherit from ConfigReader")
    
    _READERS[name] = reader_class


def list_supported_formats() -> Dict[str, list]:
    """List all supported configuration formats"""
    formats = {}
    for name, reader_class in _READERS.items():
        reader = reader_class()
        formats[name] = reader.supported_extensions
    
    return formats
