"""
Workflow Visualizer

Creates visual representations of workflows and their dependency graphs.
Supports multiple output formats including Mermaid, Graphviz, and ASCII.

Author: Assistant
"""

import json
from typing import Dict, List, Set, Optional, Tuple
from pathlib import Path
from .models import WorkflowConfig, WorkflowStep, WorkflowResult, StepResult
from .readers import get_reader


class WorkflowVisualizer:
    """Main workflow visualization engine"""
    
    def __init__(self):
        self.config: Optional[WorkflowConfig] = None
        self.result: Optional[WorkflowResult] = None
        self.dependency_graph: Dict[str, Set[str]] = {}
        self.reverse_graph: Dict[str, Set[str]] = {}
    
    def load_workflow(self, config_file: str) -> None:
        """Load workflow configuration from file"""
        reader = get_reader(config_file)
        self.config = reader.read(config_file)
        self._build_dependency_graphs()
    
    def load_workflow_config(self, config: WorkflowConfig) -> None:
        """Load workflow configuration object"""
        self.config = config
        self._build_dependency_graphs()
    
    def load_execution_result(self, result: WorkflowResult) -> None:
        """Load workflow execution result for status visualization"""
        self.result = result
    
    def _build_dependency_graphs(self) -> None:
        """Build forward and reverse dependency graphs"""
        if not self.config:
            return
        
        # Forward graph: step -> dependencies
        self.dependency_graph = {}
        # Reverse graph: step -> dependents
        self.reverse_graph = {}
        
        # Initialize graphs
        for step in self.config.steps:
            self.dependency_graph[step.name] = set(step.depends_on)
            self.reverse_graph[step.name] = set()
        
        # Build reverse graph
        for step in self.config.steps:
            for dep in step.depends_on:
                if dep in self.reverse_graph:
                    self.reverse_graph[dep].add(step.name)
    
    def generate_mermaid(self, include_status: bool = False) -> str:
        """Generate Mermaid diagram representation"""
        if not self.config:
            raise ValueError("No workflow configuration loaded")
        
        lines = ["graph TD"]
        
        # Add step nodes with styling based on status
        for step in self.config.steps:
            step_id = self._sanitize_id(step.name)
            
            # Determine node style based on step type and status
            if include_status and self.result:
                step_result = self._get_step_result(step.name)
                if step_result:
                    status = step_result.status
                    if status == "success":
                        style_class = "success"
                        icon = "✅"
                    elif status == "error":
                        style_class = "error"
                        icon = "❌"
                    elif status == "skipped":
                        style_class = "skipped"
                        icon = "⏭️"
                    else:
                        style_class = "pending"
                        icon = "⏳"
                    
                    label = f"{icon} {step.name}"
                    if step.tool_name:
                        label += f"\\n({step.tool_name})"
                    if step_result.execution_time > 0:
                        label += f"\\n{step_result.execution_time}s"
                else:
                    style_class = "pending"
                    label = f"⏳ {step.name}"
                    if step.tool_name:
                        label += f"\\n({step.tool_name})"
            else:
                style_class = "default"
                label = step.name
                if step.tool_name:
                    label += f"\\n({step.tool_name})"
            
            lines.append(f'    {step_id}["{label}"]')
            lines.append(f'    class {step_id} {style_class}')
        
        # Add dependency edges
        for step in self.config.steps:
            step_id = self._sanitize_id(step.name)
            for dep in step.depends_on:
                dep_id = self._sanitize_id(dep)
                lines.append(f'    {dep_id} --> {step_id}')
        
        # Add styling
        lines.extend([
            "",
            "    classDef default fill:#e1f5fe,stroke:#01579b,stroke-width:2px",
            "    classDef success fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px",
            "    classDef error fill:#ffebee,stroke:#c62828,stroke-width:2px", 
            "    classDef skipped fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px",
            "    classDef pending fill:#fff3e0,stroke:#ef6c00,stroke-width:2px"
        ])
        
        return "\n".join(lines)
    
    def generate_graphviz(self, include_status: bool = False) -> str:
        """Generate Graphviz DOT representation"""
        if not self.config:
            raise ValueError("No workflow configuration loaded")
        
        lines = [
            "digraph workflow {",
            "    rankdir=TB;",
            "    node [shape=box, style=rounded];",
            ""
        ]
        
        # Add nodes
        for step in self.config.steps:
            step_id = self._sanitize_id(step.name)
            
            # Build label
            label_parts = [step.name]
            if step.tool_name:
                label_parts.append(f"Tool: {step.tool_name}")
            
            if include_status and self.result:
                step_result = self._get_step_result(step.name)
                if step_result:
                    label_parts.append(f"Status: {step_result.status}")
                    if step_result.execution_time > 0:
                        label_parts.append(f"Time: {step_result.execution_time}s")
                    
                    # Set color based on status
                    if step_result.status == "success":
                        color = "lightgreen"
                    elif step_result.status == "error":
                        color = "lightcoral"
                    elif step_result.status == "skipped":
                        color = "lightgray"
                    else:
                        color = "lightyellow"
                else:
                    color = "lightblue"
            else:
                color = "lightblue"
            
            label = "\\n".join(label_parts)
            lines.append(f'    {step_id} [label="{label}", fillcolor="{color}", style="filled,rounded"];')
        
        lines.append("")
        
        # Add edges
        for step in self.config.steps:
            step_id = self._sanitize_id(step.name)
            for dep in step.depends_on:
                dep_id = self._sanitize_id(dep)
                lines.append(f'    {dep_id} -> {step_id};')
        
        lines.append("}")
        return "\n".join(lines)
    
    def generate_ascii(self, include_status: bool = False) -> str:
        """Generate ASCII art representation"""
        if not self.config:
            raise ValueError("No workflow configuration loaded")
        
        # Build execution levels based on dependencies
        levels = self._calculate_execution_levels()
        
        lines = []
        lines.append(f"Workflow: {self.config.name}")
        lines.append("=" * (len(self.config.name) + 10))
        lines.append("")
        
        if self.config.description:
            lines.append(f"Description: {self.config.description}")
            lines.append("")
        
        # Show execution levels
        for level, steps in enumerate(levels):
            lines.append(f"Level {level + 1}:")
            lines.append("-" * 20)
            
            for step_name in steps:
                step = self._get_step_by_name(step_name)
                if not step:
                    continue
                
                # Status indicator
                if include_status and self.result:
                    step_result = self._get_step_result(step_name)
                    if step_result:
                        if step_result.status == "success":
                            indicator = "✅"
                        elif step_result.status == "error":
                            indicator = "❌"
                        elif step_result.status == "skipped":
                            indicator = "⏭️"
                        else:
                            indicator = "⏳"
                        
                        timing = f" ({step_result.execution_time}s)" if step_result.execution_time > 0 else ""
                        lines.append(f"  {indicator} {step_name}{timing}")
                    else:
                        lines.append(f"  ⏳ {step_name}")
                else:
                    lines.append(f"  • {step_name}")
                
                if step.tool_name:
                    lines.append(f"    Tool: {step.tool_name}")
                
                if step.depends_on:
                    lines.append(f"    Depends on: {', '.join(step.depends_on)}")
                
                lines.append("")
            
            lines.append("")
        
        return "\n".join(lines)
    
    def generate_html_report(self, include_mermaid: bool = True) -> str:
        """Generate comprehensive HTML report"""
        if not self.config:
            raise ValueError("No workflow configuration loaded")
        
        mermaid_diagram = self.generate_mermaid(include_status=bool(self.result)) if include_mermaid else ""
        ascii_view = self.generate_ascii(include_status=bool(self.result))
        
        html = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Workflow Visualization: {self.config.name}</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background: #f5f5f5; padding: 20px; border-radius: 5px; margin-bottom: 20px; }}
        .section {{ margin: 20px 0; }}
        .ascii {{ background: #f8f8f8; padding: 15px; border-radius: 5px; font-family: monospace; white-space: pre-line; }}
        .stats {{ display: flex; gap: 20px; margin: 20px 0; }}
        .stat {{ background: #e3f2fd; padding: 10px; border-radius: 5px; text-align: center; }}
        .mermaid {{ text-align: center; margin: 20px 0; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>Workflow: {self.config.name}</h1>
        <p><strong>Version:</strong> {self.config.version}</p>
        {f'<p><strong>Description:</strong> {self.config.description}</p>' if self.config.description else ''}
    </div>
    
    {self._generate_stats_section()}
    
    {f'''
    <div class="section">
        <h2>Dependency Graph</h2>
        <div class="mermaid">
{mermaid_diagram}
        </div>
    </div>
    ''' if include_mermaid else ''}
    
    <div class="section">
        <h2>Workflow Structure</h2>
        <div class="ascii">{ascii_view}</div>
    </div>
    
    {self._generate_steps_table()}
    
    <script>
        mermaid.initialize({{ startOnLoad: true }});
    </script>
</body>
</html>
        """
        
        return html.strip()
    
    def _generate_stats_section(self) -> str:
        """Generate statistics section for HTML report"""
        if not self.config:
            return ""
        
        stats = f"""
    <div class="section">
        <h2>Workflow Statistics</h2>
        <div class="stats">
            <div class="stat">
                <strong>{len(self.config.steps)}</strong><br>
                Total Steps
            </div>
            <div class="stat">
                <strong>{self.config.settings.max_concurrency}</strong><br>
                Max Concurrency
            </div>
            <div class="stat">
                <strong>{self.config.settings.timeout}s</strong><br>
                Timeout
            </div>
        """
        
        if self.result:
            stats += f"""
            <div class="stat">
                <strong>{self.result.successful_steps}</strong><br>
                Successful
            </div>
            <div class="stat">
                <strong>{self.result.failed_steps}</strong><br>
                Failed
            </div>
            <div class="stat">
                <strong>{self.result.total_execution_time}s</strong><br>
                Total Time
            </div>
        """
        
        stats += """
        </div>
    </div>
        """
        
        return stats
    
    def _generate_steps_table(self) -> str:
        """Generate steps table for HTML report"""
        if not self.config:
            return ""
        
        table = """
    <div class="section">
        <h2>Step Details</h2>
        <table border="1" style="width: 100%; border-collapse: collapse;">
            <tr style="background: #f5f5f5;">
                <th>Step Name</th>
                <th>Type</th>
                <th>Tool</th>
                <th>Dependencies</th>
        """
        
        if self.result:
            table += "<th>Status</th><th>Time (s)</th><th>Error</th>"
        
        table += "</tr>"
        
        for step in self.config.steps:
            deps = ", ".join(step.depends_on) if step.depends_on else "None"
            
            table += f"""
            <tr>
                <td>{step.name}</td>
                <td>{step.type}</td>
                <td>{step.tool_name or 'N/A'}</td>
                <td>{deps}</td>
            """
            
            if self.result:
                step_result = self._get_step_result(step.name)
                if step_result:
                    status_color = {
                        'success': '#e8f5e8',
                        'error': '#ffebee',
                        'skipped': '#f3e5f5'
                    }.get(step_result.status, '#fff3e0')
                    
                    table += f"""
                <td style="background: {status_color}">{step_result.status}</td>
                <td>{step_result.execution_time}</td>
                <td>{step_result.error or ''}</td>
                    """
                else:
                    table += "<td>Not executed</td><td>-</td><td>-</td>"
            
            table += "</tr>"
        
        table += """
        </table>
    </div>
        """
        
        return table
    
    def _calculate_execution_levels(self) -> List[List[str]]:
        """Calculate execution levels based on dependencies"""
        if not self.config:
            return []
        
        levels = []
        remaining_steps = {step.name for step in self.config.steps}
        completed_steps = set()
        
        while remaining_steps:
            current_level = []
            
            # Find steps that can execute (all dependencies completed)
            for step_name in list(remaining_steps):
                step = self._get_step_by_name(step_name)
                if step and all(dep in completed_steps for dep in step.depends_on):
                    current_level.append(step_name)
            
            if not current_level:
                # Circular dependency or error
                current_level = list(remaining_steps)
            
            levels.append(current_level)
            
            for step_name in current_level:
                remaining_steps.remove(step_name)
                completed_steps.add(step_name)
        
        return levels
    
    def _get_step_by_name(self, name: str) -> Optional[WorkflowStep]:
        """Get step by name"""
        if not self.config:
            return None
        
        for step in self.config.steps:
            if step.name == name:
                return step
        return None
    
    def _get_step_result(self, step_name: str) -> Optional[StepResult]:
        """Get step result by name"""
        if not self.result:
            return None
        
        for step_result in self.result.steps:
            if step_result.step_name == step_name:
                return step_result
        return None
    
    def _sanitize_id(self, name: str) -> str:
        """Sanitize step name for use as ID"""
        return name.replace(" ", "_").replace("-", "_").replace(".", "_")


def visualize_workflow_file(config_file: str, output_format: str = "html", 
                           output_file: Optional[str] = None, 
                           result_file: Optional[str] = None) -> str:
    """Convenience function to visualize workflow from file"""
    
    visualizer = WorkflowVisualizer()
    visualizer.load_workflow(config_file)
    
    # Load execution result if provided
    if result_file and Path(result_file).exists():
        with open(result_file, 'r') as f:
            result_data = json.load(f)
            if 'workflow_result' in result_data:
                result_dict = result_data['workflow_result']
                result = WorkflowResult(**result_dict)
                visualizer.load_execution_result(result)
    
    # Generate visualization
    if output_format.lower() == "mermaid":
        content = visualizer.generate_mermaid(include_status=bool(visualizer.result))
    elif output_format.lower() == "graphviz":
        content = visualizer.generate_graphviz(include_status=bool(visualizer.result))
    elif output_format.lower() == "ascii":
        content = visualizer.generate_ascii(include_status=bool(visualizer.result))
    elif output_format.lower() == "html":
        content = visualizer.generate_html_report()
    else:
        raise ValueError(f"Unsupported output format: {output_format}")
    
    # Write to file if specified
    if output_file:
        with open(output_file, 'w') as f:
            f.write(content)
        print(f"Visualization saved to: {output_file}")
    
    return content
