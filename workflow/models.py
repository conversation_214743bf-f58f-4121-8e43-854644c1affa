"""
Workflow Data Models

Pydantic models for workflow configuration and execution results.

Author: Assistant
"""

from typing import Dict, Any, List, Optional, Union, Callable, Awaitable
from pydantic import BaseModel, Field, validator
from enum import Enum
import datetime


class StepType(str, Enum):
    """Types of workflow steps"""
    TOOL = "tool"
    PARALLEL = "parallel"
    CONDITIONAL = "conditional"
    LOOP = "loop"


class WorkflowStep(BaseModel):
    """A single step in a workflow"""
    name: str = Field(..., description="Unique name for this step")
    type: StepType = Field(StepType.TOOL, description="Type of step")
    tool_name: Optional[str] = Field(None, description="Name of tool to execute (for tool steps)")
    input_data: Dict[str, Any] = Field(default_factory=dict, description="Input data for the step")
    depends_on: List[str] = Field(default_factory=list, description="Steps this step depends on")
    condition: Optional[str] = Field(None, description="Condition for conditional steps")
    parallel_steps: List['WorkflowStep'] = Field(default_factory=list, description="Steps to run in parallel")
    
    class Config:
        use_enum_values = True

    @validator('tool_name')
    def validate_tool_name_for_tool_steps(cls, v, values):
        """Validate that tool steps have a tool_name"""
        if values.get('type') == StepType.TOOL and not v:
            raise ValueError("Tool steps must specify a tool_name")
        return v


# Forward reference resolution
WorkflowStep.model_rebuild()


class WorkflowSettings(BaseModel):
    """Global workflow settings"""
    max_concurrency: int = Field(3, ge=1, le=50, description="Maximum concurrent executions")
    timeout: int = Field(300, ge=1, description="Overall timeout in seconds")
    fail_fast: bool = Field(False, description="Stop execution on first failure")
    continue_on_error: bool = Field(True, description="Continue workflow even if some steps fail")
    
    class Config:
        extra = "allow"  # Allow additional settings


class WorkflowOutput(BaseModel):
    """Workflow output configuration"""
    summary_file: Optional[str] = Field(None, description="Path to write execution summary")
    log_file: Optional[str] = Field(None, description="Path to write detailed logs")
    format: str = Field("json", description="Output format (json, yaml)")
    
    class Config:
        extra = "allow"  # Allow additional output settings


class WorkflowConfig(BaseModel):
    """Complete workflow configuration"""
    name: str = Field(..., description="Workflow name")
    description: Optional[str] = Field(None, description="Workflow description")
    version: str = Field("1.0", description="Workflow version")
    steps: List[WorkflowStep] = Field(..., min_items=1, description="Workflow steps")
    settings: WorkflowSettings = Field(default_factory=WorkflowSettings, description="Workflow settings")
    output: WorkflowOutput = Field(default_factory=WorkflowOutput, description="Output configuration")
    variables: Dict[str, Any] = Field(default_factory=dict, description="Workflow variables")
    
    @validator('steps')
    def validate_step_dependencies(cls, v):
        """Validate that step dependencies exist"""
        step_names = {step.name for step in v}
        
        for step in v:
            for dep in step.depends_on:
                if dep not in step_names:
                    raise ValueError(f"Step '{step.name}' depends on non-existent step '{dep}'")
        
        return v


class WorkflowEvent(BaseModel):
    """Event fired during workflow execution"""
    event_type: str = Field(..., description="Type of event (step_completed, step_started, workflow_started, workflow_completed)")
    timestamp: datetime.datetime = Field(default_factory=datetime.datetime.now, description="When the event occurred")
    workflow_name: str = Field(..., description="Name of the workflow")
    step_name: Optional[str] = Field(None, description="Name of the step (if step-related event)")
    step_result: Optional['StepResult'] = Field(None, description="Step result (for step_completed events)")
    workflow_progress: Dict[str, Any] = Field(default_factory=dict, description="Current workflow progress")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional event metadata")

    class Config:
        arbitrary_types_allowed = True
        use_enum_values = True


# Type definitions for event callbacks
EventCallback = Callable[[WorkflowEvent], None]
AsyncEventCallback = Callable[[WorkflowEvent], Awaitable[None]]


class StepResult(BaseModel):
    """Result from executing a single workflow step"""
    step_name: str = Field(..., description="Name of the executed step")
    step_type: StepType = Field(..., description="Type of step")
    status: str = Field(..., description="Execution status (success, error, skipped)")
    execution_time: float = Field(..., ge=0, description="Execution time in seconds")
    data: Any = Field(None, description="Step result data")
    error: Optional[str] = Field(None, description="Error message if step failed")
    tool_name: Optional[str] = Field(None, description="Tool that was executed")

    class Config:
        arbitrary_types_allowed = True
        use_enum_values = True


class WorkflowResult(BaseModel):
    """Result from executing a complete workflow"""
    workflow_name: str = Field(..., description="Name of the executed workflow")
    config_file: Optional[str] = Field(None, description="Path to configuration file")
    total_steps: int = Field(..., ge=0, description="Total number of steps")
    executed_steps: int = Field(..., ge=0, description="Number of steps executed")
    successful_steps: int = Field(..., ge=0, description="Number of successful steps")
    failed_steps: int = Field(..., ge=0, description="Number of failed steps")
    skipped_steps: int = Field(..., ge=0, description="Number of skipped steps")
    total_execution_time: float = Field(..., ge=0, description="Total execution time")
    status: str = Field(..., description="Overall workflow status")
    steps: List[StepResult] = Field(..., description="Individual step results")
    summary: str = Field(..., description="Execution summary")
    dry_run: bool = Field(False, description="Whether this was a dry run")
    
    class Config:
        arbitrary_types_allowed = True


# Forward reference resolution for WorkflowEvent
WorkflowEvent.model_rebuild()
