#!/usr/bin/env python3
"""
Workflow Runner

A simple CLI script to execute workflows using the standalone workflow system.

Usage:
    python run_workflow.py <workflow_file> [--dry-run] [--variables key=value ...]

Examples:
    python run_workflow.py workflows/repo_clone_workflow.yaml --dry-run
    python run_workflow.py workflows/repo_clone_workflow.yaml --variables author_name="<PERSON>"
"""

import sys
import asyncio
import argparse
import tempfile
from pathlib import Path

# Add current directory to path
sys.path.append('.')

from workflow import WorkflowEngine


def parse_variables(var_strings):
    """Parse variable strings in format key=value"""
    variables = {}
    for var_string in var_strings:
        if '=' not in var_string:
            print(f"⚠️  Invalid variable format: {var_string} (expected key=value)")
            continue
        
        key, value = var_string.split('=', 1)
        variables[key.strip()] = value.strip()
    
    return variables


async def main():
    parser = argparse.ArgumentParser(description="Execute workflows")
    parser.add_argument("workflow_file", help="Path to workflow configuration file")
    parser.add_argument("--dry-run", action="store_true", help="Perform dry run without executing")
    parser.add_argument("--variables", nargs="*", default=[], help="Override variables (key=value format)")
    parser.add_argument("--temp-dir", action="store_true", help="Use temporary directory for destinations")
    
    args = parser.parse_args()
    
    # Parse variables
    variables = parse_variables(args.variables)
    
    # Override base destination if using temp directory
    if args.temp_dir:
        temp_dir = tempfile.mkdtemp()
        variables['base_destination'] = f"{temp_dir}/cloned-repos"
        print(f"🗂️  Using temporary directory: {temp_dir}")
    
    try:
        # Create workflow engine
        engine = WorkflowEngine()
        
        print(f"🚀 {'Dry run' if args.dry_run else 'Executing'} workflow: {args.workflow_file}")
        if variables:
            print(f"📝 Variables: {variables}")
        
        # Execute workflow
        result = await engine.execute_from_file(
            args.workflow_file,
            dry_run=args.dry_run,
            variables=variables
        )
        
        # Print results
        print(f"\n{'='*60}")
        print(f"🎯 Workflow Results")
        print(f"{'='*60}")
        
        print(f"📊 Summary:")
        print(f"   • Workflow: {result.workflow_name}")
        print(f"   • Status: {result.status}")
        print(f"   • Total steps: {result.total_steps}")
        print(f"   • Executed: {result.executed_steps}")
        print(f"   • Successful: {result.successful_steps}")
        print(f"   • Failed: {result.failed_steps}")
        print(f"   • Skipped: {result.skipped_steps}")
        print(f"   • Execution time: {result.total_execution_time}s")
        print(f"   • Summary: {result.summary}")
        
        print(f"\n📋 Step Details:")
        for i, step in enumerate(result.steps, 1):
            status_icon = {
                'success': '✅',
                'error': '❌',
                'skipped': '⏭️',
                'dry_run': '🔍'
            }.get(step.status, '❓')
            
            print(f"   {status_icon} Step {i}: {step.step_name}")
            print(f"      • Type: {step.step_type}")
            print(f"      • Status: {step.status}")
            print(f"      • Time: {step.execution_time}s")
            
            if step.tool_name:
                print(f"      • Tool: {step.tool_name}")
            
            if step.error:
                print(f"      • Error: {step.error}")
            elif step.data and args.dry_run and 'would_execute' in step.data:
                exec_data = step.data['would_execute']
                print(f"      • Would execute: {exec_data.get('tool_name', 'N/A')}")
                if exec_data.get('input_data'):
                    print(f"      • Input: {exec_data['input_data']}")
        
        # Overall result
        if result.status == "success":
            print(f"\n🎉 Workflow completed successfully!")
        elif result.status == "dry_run":
            print(f"\n🔍 Dry run completed - workflow is ready to execute")
        elif result.status == "partial":
            print(f"\n⚠️  Workflow completed with some failures")
        else:
            print(f"\n❌ Workflow failed")
        
        # Exit with appropriate code
        if result.status in ["success", "dry_run"]:
            sys.exit(0)
        else:
            sys.exit(1)
            
    except FileNotFoundError as e:
        print(f"❌ File not found: {e}")
        sys.exit(1)
    except ValueError as e:
        print(f"❌ Configuration error: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
