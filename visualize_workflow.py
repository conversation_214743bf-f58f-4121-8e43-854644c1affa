#!/usr/bin/env python3
"""
Workflow Visualizer CLI

A command-line tool for visualizing workflows and their dependency graphs.
Supports multiple output formats including Mermaid, Graphviz, ASCII, and HTML.

Usage:
    python visualize_workflow.py <workflow_file> [options]

Examples:
    python visualize_workflow.py workflows/simple_demo.yaml --format html --output demo.html
    python visualize_workflow.py workflows/repo_clone_workflow.yaml --format mermaid
    python visualize_workflow.py workflows/simple_demo.yaml --format ascii --result demo_workflow_summary.json
"""

import sys
import argparse
from pathlib import Path

# Add current directory to path
sys.path.append('.')

from workflow import WorkflowVisualizer, visualize_workflow_file


def main():
    parser = argparse.ArgumentParser(description="Visualize workflow configurations and execution results")
    parser.add_argument("workflow_file", help="Path to workflow configuration file")
    parser.add_argument("--format", "-f", choices=["html", "mermaid", "graphviz", "ascii"], 
                       default="html", help="Output format (default: html)")
    parser.add_argument("--output", "-o", help="Output file path (default: print to stdout)")
    parser.add_argument("--result", "-r", help="Path to workflow execution result JSON file")
    parser.add_argument("--open", action="store_true", help="Open HTML output in browser (HTML format only)")
    
    args = parser.parse_args()
    
    # Validate workflow file
    if not Path(args.workflow_file).exists():
        print(f"❌ Workflow file not found: {args.workflow_file}")
        sys.exit(1)
    
    # Validate result file if provided
    if args.result and not Path(args.result).exists():
        print(f"❌ Result file not found: {args.result}")
        sys.exit(1)
    
    try:
        print(f"🎨 Generating {args.format.upper()} visualization for: {args.workflow_file}")
        
        # Generate visualization
        content = visualize_workflow_file(
            config_file=args.workflow_file,
            output_format=args.format,
            output_file=args.output,
            result_file=args.result
        )
        
        # Print to stdout if no output file specified
        if not args.output:
            print("\n" + "="*60)
            print(f"📊 {args.format.upper()} Visualization")
            print("="*60)
            print(content)
        
        # Open in browser if requested and HTML format
        if args.open and args.format == "html" and args.output:
            try:
                import webbrowser
                file_path = Path(args.output).resolve()
                webbrowser.open(f"file://{file_path}")
                print(f"🌐 Opened in browser: {file_path}")
            except Exception as e:
                print(f"⚠️  Could not open in browser: {e}")
        
        print(f"\n✅ Visualization generated successfully!")
        
        # Show additional info if result was included
        if args.result:
            print(f"📈 Execution status included from: {args.result}")
        
    except FileNotFoundError as e:
        print(f"❌ File not found: {e}")
        sys.exit(1)
    except ValueError as e:
        print(f"❌ Configuration error: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


def demo_all_formats():
    """Generate demo visualizations in all formats"""
    
    workflow_files = [
        "workflows/simple_demo.yaml",
        "workflows/repo_clone_workflow.yaml"
    ]
    
    formats = ["ascii", "mermaid", "graphviz", "html"]
    
    print("🎨 Generating demo visualizations in all formats...")
    print("="*60)
    
    for workflow_file in workflow_files:
        if not Path(workflow_file).exists():
            print(f"⚠️  Skipping {workflow_file} (not found)")
            continue
        
        workflow_name = Path(workflow_file).stem
        print(f"\n📋 Processing: {workflow_name}")
        
        for fmt in formats:
            try:
                output_file = f"demo_{workflow_name}_{fmt}.{'html' if fmt == 'html' else 'txt'}"
                
                content = visualize_workflow_file(
                    config_file=workflow_file,
                    output_format=fmt,
                    output_file=output_file
                )
                
                print(f"  ✅ {fmt.upper()}: {output_file}")
                
            except Exception as e:
                print(f"  ❌ {fmt.upper()}: {e}")
    
    print(f"\n🎉 Demo generation complete!")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--demo":
        demo_all_formats()
    else:
        main()
