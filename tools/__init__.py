"""
Tools Package

Main entry point for the tool system. Provides access to all tools and the base system.

Author: Assistant
"""

# Import the base system
from .base import (
    execute,
    get_all_tools,
    get_namespaces,
    get_namespace_summary,
    get_tool_definition,
    tool_exists,
    search_tools,
    print_tool_summary,
    print_tool_details,
    BaseTool,
    ToolOutput,
    ToolStatus,
    register_tool
)

# Import tool implementations to trigger registration
from . import terminal_tools
from . import file_tools
from . import copier_tools
from . import orchestration_tools
from . import workflow_tools

__all__ = [
    # Main execution function
    'execute',
    
    # Discovery and info functions
    'get_all_tools',
    'get_namespaces', 
    'get_namespace_summary',
    'get_tool_definition',
    'tool_exists',
    'search_tools',
    'print_tool_summary',
    'print_tool_details',
    
    # Base classes for creating new tools
    'BaseTool',
    'ToolOutput',
    'ToolStatus', 
    'register_tool',
    
    # Tool modules
    'terminal_tools',
    'file_tools',
    'copier_tools',
    'orchestration_tools'
]
