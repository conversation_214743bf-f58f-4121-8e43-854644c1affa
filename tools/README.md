# Tool System

This directory contains a unified tool system with namespaces and a single entry point for all tools. Tools can be used programmatically by LLMs and other processes.

## Files

- **`base/`** - Core tool system infrastructure
  - **`base_tool.py`** - Base tool classes, registry, and core functionality
  - **`tool_manager.py`** - Tool discovery, management, and execution
- **`terminal_tools.py`** - Terminal command execution tools (namespace: terminal)
- **`file_tools.py`** - File system operation tools (namespace: file)
- **`__init__.py`** - Main package entry point
- **`README.md`** - This documentation file

## Quick Start

### Single Entry Point Usage

```python
# Option 1: Import from base system directly
from tools.base.tool_manager import execute, get_all_tools, get_namespaces

# Option 2: Import from main package (recommended)
from tools import execute, get_all_tools, get_namespaces

# List available tools
tools = get_all_tools()
namespaces = get_namespaces()

# Execute a terminal command
result = execute("terminal.execute", {
    "command": "ls -la",
    "timeout": 10
})
print(f"Status: {result.status.value}")
print(f"Output: {result.data.stdout}")

# Execute multiple commands
batch_result = execute("terminal.batch", {
    "commands": ["echo 'Starting'", "date", "echo 'Done'"],
    "timeout": 10
})

# File operations
write_result = execute("file.write", {
    "path": "test.txt",
    "content": "Hello World!",
    "overwrite": True
})

read_result = execute("file.read", {"path": "test.txt"})
```

### Async Usage

```python
from tools import execute

# Async execution
result = await execute("terminal.execute", {
    "command": "echo 'Hello'"
}, async_mode=True)
```

## API Reference

### ToolOutput

All tool executions return a `ToolOutput` Pydantic model with:

- `status`: ToolStatus enum (SUCCESS, ERROR, TIMEOUT)
- `data`: Tool-specific result data (structured Pydantic models)
- `error`: Error message (if any)
- `execution_time`: Time taken in seconds (validated >= 0)
- `metadata`: Additional metadata (if any)

**Methods:**
- `is_success()`: Check if execution was successful
- `is_error()`: Check if execution failed
- `is_timeout()`: Check if execution timed out
- `to_dict()`: Convert to dictionary
- `to_json()`: Convert to JSON string

### Main Functions

#### `execute(tool_name, input_data, async_mode=False)`

Main entry point for tool execution.

**Parameters:**
- `tool_name` (str): Full tool name (namespace.name)
- `input_data` (dict): Input parameters for the tool
- `async_mode` (bool): Return coroutine for async execution

**Returns:**
- `ToolOutput` (sync) or coroutine (async)

#### Tool Discovery Functions

- `get_all_tools()`: List all available tools
- `get_namespaces()`: List all namespaces
- `get_tools_by_namespace(namespace)`: Get tools in a specific namespace
- `get_tool_definition(tool_name)`: Get tool definition and schema
- `search_tools(query)`: Search tools by name or description

## Available Tools

### Terminal Namespace (`terminal`)

#### `terminal.execute`
Execute a single terminal command.

**Input:**
- `command` (str): Command to execute
- `working_dir` (str, optional): Working directory
- `env_vars` (dict, optional): Environment variables
- `timeout` (int, optional): Timeout in seconds (default: 30)
- `shell` (bool, optional): Use shell execution (default: true)

#### `terminal.batch`
Execute multiple commands in sequence.

**Input:**
- `commands` (list): List of commands to execute
- `working_dir` (str, optional): Working directory
- `env_vars` (dict, optional): Environment variables
- `timeout` (int, optional): Timeout per command (default: 30)
- `shell` (bool, optional): Use shell execution (default: true)
- `stop_on_error` (bool, optional): Stop on first error (default: false)

### File Namespace (`file`)

#### `file.read`
Read file contents.

**Input:**
- `path` (str): File path to read
- `encoding` (str, optional): File encoding (default: utf-8)
- `max_size` (int, optional): Max file size in bytes (default: 1MB)

#### `file.write`
Write content to a file.

**Input:**
- `path` (str): File path to write
- `content` (str): Content to write
- `encoding` (str, optional): File encoding (default: utf-8)
- `create_dirs` (bool, optional): Create parent directories (default: false)
- `overwrite` (bool, optional): Overwrite existing file (default: false)

#### `file.list`
List directory contents.

**Input:**
- `path` (str, optional): Directory path (default: current directory)
- `recursive` (bool, optional): List recursively (default: false)
- `include_hidden` (bool, optional): Include hidden files (default: false)
- `pattern` (str, optional): Glob pattern to filter files

#### `file.delete`
Delete a file or directory.

**Input:**
- `path` (str): Path to delete
- `recursive` (bool, optional): Delete directories recursively (default: false)
- `force` (bool, optional): Force deletion (default: false)

## Pydantic Models

The tool system uses Pydantic models for structured data with validation, serialization, and rich features:

### Core Models

- **`ToolOutput`**: Base output for all tools with status, data, error, and timing information
- **`CommandResult`**: Terminal command execution results with stdout, stderr, and exit codes
- **`FileInfo`**: File/directory information with path, size, type, and modification time
- **`FileReadResult`**: File reading results with content, encoding, and metadata
- **`FileWriteResult`**: File writing results with path, size, and creation status
- **`FileListResult`**: Directory listing with file counts and detailed file information
- **`FileDeleteResult`**: File deletion results with path and operation details

### Automatic Schema Derivation

The tool system automatically generates JSON schemas from Pydantic input models:

```python
# Instead of manually defining schemas:
def get_input_schema(self) -> Dict[str, Any]:
    return {
        "type": "object",
        "properties": {
            "command": {"type": "string", "description": "Command to execute"},
            "timeout": {"type": "integer", "minimum": 1, "maximum": 300}
        },
        "required": ["command"]
    }

# Simply define a Pydantic model:
class TerminalExecuteInput(BaseModel):
    command: str = Field(..., description="Command to execute")
    timeout: int = Field(30, ge=1, le=300, description="Timeout in seconds")

def get_input_model(self):
    return TerminalExecuteInput  # Schema auto-generated!
```

### Pydantic Benefits

- **Automatic Schema Generation**: JSON schemas derived from Pydantic models
- **Rich Validation**: Field constraints, type checking, and custom validators
- **Serialization**: Built-in JSON serialization with `to_json()` and `to_dict()`
- **Documentation**: Rich field descriptions and examples in schemas
- **Type Safety**: Strong typing with IDE support and runtime validation
- **Methods**: Custom methods like `is_success()`, `has_output()`, etc.
- **Examples**: Schema examples for better API documentation

### Example Usage

```python
# Execute a tool and get structured results
result = execute("file.read", {"path": "example.txt"})

# Access structured data
file_data = result.data  # FileReadResult model
print(f"File size: {file_data.size}")
print(f"Encoding: {file_data.encoding}")

# Use validation and methods
if result.is_success() and file_data.size > 0:
    print("File read successfully!")

# Serialize to JSON
json_output = result.to_json()
```

## Adding New Tools

To add new tools to the system:

1. **Create a new tool file** in the `tools/` directory following the naming pattern `*_tools.py`
2. **Import the base system**:
   ```python
   from base import BaseTool, ToolOutput, ToolStatus, register_tool
   from pydantic import BaseModel, Field
   ```
3. **Create Pydantic input and output models**:
   ```python
   class MyToolInput(BaseModel):
       param1: str = Field(..., description="Parameter description")
       param2: int = Field(10, ge=1, le=100, description="Number between 1-100")

       class Config:
           json_schema_extra = {
               "examples": [
                   {"param1": "example", "param2": 50}
               ]
           }

   class MyToolResult(BaseModel):
       result: str = Field(..., description="The tool result")
       processed_at: float = Field(..., description="Processing timestamp")

       class Config:
           arbitrary_types_allowed = True
   ```
4. **Create tool classes** inheriting from `BaseTool`:
   ```python
   class MyTool(BaseTool):
       def __init__(self):
           super().__init__(namespace="my_namespace", name="my_tool")

       def get_description(self) -> str:
           return "Description of what this tool does"

       def get_input_model(self):
           return MyToolInput  # Automatic schema derivation!

       async def execute(self, input_data: Dict[str, Any]) -> ToolOutput:
           # Parse and validate input automatically
           parsed_input = self.parse_input(input_data)

           # Tool implementation with validated Pydantic input
           result = MyToolResult(
               result=f"Processed: {parsed_input.param1}",
               processed_at=time.time()
           )
           return ToolOutput(status=ToolStatus.SUCCESS, data=result)
   ```
5. **Register the tool**:
   ```python
   register_tool(MyTool())
   ```
6. **Update the main `__init__.py`** to import your new tool module:
   ```python
   import your_new_tools
   ```
7. **Tools are automatically discovered** and available via `execute("my_namespace.my_tool", input_data)`

## Directory Structure

```
tools/
├── base/                     # Core tool system infrastructure
│   ├── __init__.py          # Base package exports
│   ├── base_tool.py         # Base classes and registry
│   └── tool_manager.py      # Tool discovery and execution
├── terminal_tools.py         # Terminal namespace tools
├── file_tools.py            # File namespace tools
├── your_tools.py            # Your custom tools (add here)
├── __init__.py              # Main package entry point
└── README.md                # This documentation
```

## Programmatic Usage Only

These tools are designed for programmatic use only. They do not provide command-line interfaces as they are intended to be called directly by LLMs and other processes.

## Safety Features

- **Timeout Control**: Prevents hanging commands
- **Working Directory Isolation**: Commands run in specified directories
- **Environment Variable Control**: Safe environment variable handling
- **Non-shell Execution**: Option to disable shell for untrusted input
- **Error Handling**: Comprehensive error reporting and recovery

## Usage Examples

### System Information Gathering

```python
# Get system info for LLM analysis
commands = [
    "uname -a",           # System info
    "python --version",   # Python version
    "df -h",             # Disk usage
    "free -h"            # Memory usage (Linux)
]

result = execute_batch_sync(commands, timeout=10)
for cmd_result in result['results']:
    print(f"{cmd_result['command']}: {cmd_result['stdout'].strip()}")
```

### Git Operations

```python
# Check git repository status
git_commands = [
    "git status --porcelain",
    "git log --oneline -5",
    "git branch --show-current"
]

result = execute_batch_sync(
    git_commands, 
    working_dir="/path/to/repo",
    stop_on_error=True
)
```

### File Operations

```python
# Safe file operations
result = execute_command_sync(
    "find . -name '*.py' | head -10",
    working_dir="/project/src",
    timeout=15
)
```

## Error Handling

The tools provide detailed error information:

```python
result = execute_command_sync("nonexistent_command")
if result.status == "error":
    print(f"Command failed: {result.stderr}")
    print(f"Exit code: {result.return_code}")
elif result.status == "timeout":
    print("Command timed out")
```

## Integration with LLMs

These tools are designed to be easily integrated with LLM workflows:

1. **Structured Output**: All results are structured for easy parsing
2. **JSON Support**: Optional JSON output for API integration
3. **Batch Processing**: Execute multiple related commands efficiently
4. **Safety Controls**: Timeouts and error handling prevent system issues
5. **Detailed Reporting**: Rich information for LLM analysis

## Best Practices

1. **Always set timeouts** for commands that might hang
2. **Use working directories** to isolate command execution
3. **Handle errors gracefully** by checking the status field
4. **Use batch execution** for related commands to improve efficiency
5. **Consider non-shell execution** for untrusted input
6. **Set appropriate environment variables** for consistent behavior

## Security Considerations

- Commands are executed with the same privileges as the calling process
- Use `shell=False` for untrusted input to prevent shell injection
- Always validate command inputs when possible
- Consider using working directory restrictions
- Monitor execution times and set appropriate timeouts
