"""
Workflow Tools

Tools for executing complex workflows that combine multiple tools in sequence.

Author: Assistant
"""

import asyncio
import json
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field, validator
from pathlib import Path

from .base import BaseTool, ToolOutput, ToolStatus, register_tool, execute_tool


class WorkflowInput(BaseModel):
    """Input model for workflow execution"""
    config_file: str = Field(..., description="Path to YAML configuration file")
    dry_run: bool = Field(False, description="If true, show what would be executed without running")
    override_destination: Optional[str] = Field(None, description="Override destination directory for all repos")
    
    class Config:
        json_schema_extra = {
            "examples": [
                {
                    "config_file": "example_config.yaml",
                    "dry_run": False
                },
                {
                    "config_file": "./configs/my_workflow.yaml",
                    "dry_run": True,
                    "override_destination": "./temp-clones"
                }
            ]
        }

    @validator('config_file')
    def validate_config_file(cls, v):
        """Validate config file exists"""
        if not v:
            raise ValueError("Config file path cannot be empty")
        
        path = Path(v)
        if not path.exists():
            raise ValueError(f"Config file does not exist: {v}")
        
        return v


class RepositoryCloneStep(BaseModel):
    """A single repository clone step"""
    name: str
    url: str
    destination: str
    type: str = "copier"
    checkout: Optional[str] = None
    data: Dict[str, Any] = {}
    overwrite: bool = True


class WorkflowConfig(BaseModel):
    """Parsed workflow configuration"""
    repositories: List[RepositoryCloneStep]
    workflow: Dict[str, Any] = {}
    output: Dict[str, Any] = {}


class WorkflowStepResult(BaseModel):
    """Result from a single workflow step"""
    step_name: str
    step_type: str
    status: str
    execution_time: float
    data: Any = None
    error: Optional[str] = None
    
    class Config:
        arbitrary_types_allowed = True


class WorkflowResult(BaseModel):
    """Result from workflow execution"""
    config_file: str
    total_steps: int
    successful_steps: int
    failed_steps: int
    total_execution_time: float
    steps: List[WorkflowStepResult]
    summary: str
    dry_run: bool = False
    
    class Config:
        arbitrary_types_allowed = True


class RepoCloneWorkflowTool(BaseTool):
    """Tool for executing repository cloning workflows from YAML configuration"""

    def __init__(self):
        super().__init__(namespace="workflow", name="repo_clone")

    def get_description(self) -> str:
        return "Execute a repository cloning workflow from YAML configuration file"

    def get_input_model(self):
        return WorkflowInput
    
    async def execute(self, input_data: Dict[str, Any]) -> ToolOutput:
        start_time = asyncio.get_event_loop().time()

        # Parse input using Pydantic model
        parsed_input = self.parse_input(input_data)

        config_file = parsed_input.config_file
        dry_run = parsed_input.dry_run
        override_destination = parsed_input.override_destination
        
        try:
            # Step 1: Read the YAML configuration file
            print(f"📖 Reading configuration from {config_file}...")
            
            file_result = await execute_tool("file.read", {"path": config_file})
            if file_result.status != ToolStatus.SUCCESS.value:
                return ToolOutput(
                    status=ToolStatus.ERROR,
                    error=f"Failed to read config file: {file_result.error}",
                    execution_time=round(asyncio.get_event_loop().time() - start_time, 3)
                )
            
            # Step 2: Parse YAML content
            try:
                import yaml
                config_data = yaml.safe_load(file_result.data.content)
                workflow_config = WorkflowConfig(**config_data)
            except Exception as e:
                return ToolOutput(
                    status=ToolStatus.ERROR,
                    error=f"Failed to parse YAML config: {str(e)}",
                    execution_time=round(asyncio.get_event_loop().time() - start_time, 3)
                )
            
            print(f"✅ Configuration loaded: {len(workflow_config.repositories)} repositories to clone")
            
            # Step 3: Prepare clone operations
            clone_tools = []
            for i, repo in enumerate(workflow_config.repositories):
                # Override destination if specified
                destination = override_destination or repo.destination
                
                clone_tool = {
                    "tool_name": "copier.clone",
                    "input_data": {
                        "template_url": repo.url,
                        "destination": destination,
                        "data": repo.data,
                        "overwrite": repo.overwrite
                    },
                    "id": f"clone_{repo.name}"
                }
                
                # Add optional checkout parameter
                if repo.checkout:
                    clone_tool["input_data"]["checkout"] = repo.checkout
                
                clone_tools.append(clone_tool)
            
            # Step 4: Execute workflow
            if dry_run:
                print("🔍 DRY RUN - Would execute the following operations:")
                steps = []
                for i, tool in enumerate(clone_tools):
                    print(f"  {i+1}. Clone {workflow_config.repositories[i].url}")
                    print(f"     → {tool['input_data']['destination']}")
                    print(f"     → Data: {tool['input_data']['data']}")
                    
                    steps.append(WorkflowStepResult(
                        step_name=tool["id"],
                        step_type="copier.clone",
                        status="dry_run",
                        execution_time=0.0,
                        data={"would_execute": tool["input_data"]}
                    ))
                
                result = WorkflowResult(
                    config_file=config_file,
                    total_steps=len(steps),
                    successful_steps=len(steps),
                    failed_steps=0,
                    total_execution_time=round(asyncio.get_event_loop().time() - start_time, 3),
                    steps=steps,
                    summary=f"Dry run completed: {len(steps)} operations would be executed",
                    dry_run=True
                )
                
                return ToolOutput(
                    status=ToolStatus.SUCCESS,
                    data=result,
                    execution_time=round(asyncio.get_event_loop().time() - start_time, 3)
                )
            
            # Step 5: Execute concurrent cloning
            print(f"🚀 Starting concurrent cloning of {len(clone_tools)} repositories...")
            
            workflow_settings = workflow_config.workflow
            concurrent_result = await execute_tool("orchestration.concurrent", {
                "tools": clone_tools,
                "timeout": workflow_settings.get("timeout", 300),
                "max_concurrency": workflow_settings.get("max_concurrency", 3),
                "fail_fast": workflow_settings.get("fail_fast", False)
            })
            
            # Step 6: Process results
            steps = []
            for tool_result in concurrent_result.data.results:
                steps.append(WorkflowStepResult(
                    step_name=tool_result.tool_name,
                    step_type="copier.clone",
                    status=tool_result.status,
                    execution_time=tool_result.execution_time,
                    data=tool_result.data,
                    error=tool_result.error
                ))
            
            # Step 7: Generate summary
            successful = len([s for s in steps if s.status == ToolStatus.SUCCESS.value])
            failed = len(steps) - successful
            
            summary = f"Workflow completed: {successful}/{len(steps)} repositories cloned successfully"
            if failed > 0:
                summary += f", {failed} failed"
            
            result = WorkflowResult(
                config_file=config_file,
                total_steps=len(steps),
                successful_steps=successful,
                failed_steps=failed,
                total_execution_time=round(asyncio.get_event_loop().time() - start_time, 3),
                steps=steps,
                summary=summary,
                dry_run=False
            )
            
            # Step 8: Write summary if configured
            output_config = workflow_config.output
            if output_config.get("summary_file"):
                summary_data = {
                    "workflow_result": result.dict(),
                    "timestamp": asyncio.get_event_loop().time(),
                    "config_file": config_file
                }
                
                await execute_tool("file.write", {
                    "path": output_config["summary_file"],
                    "content": json.dumps(summary_data, indent=2),
                    "overwrite": True
                })
                print(f"📄 Summary written to {output_config['summary_file']}")
            
            # Determine overall status
            if failed == 0:
                status = ToolStatus.SUCCESS
            else:
                status = ToolStatus.ERROR
            
            print(f"✅ {summary}")
            
            return ToolOutput(
                status=status,
                data=result,
                execution_time=round(asyncio.get_event_loop().time() - start_time, 3)
            )
                
        except Exception as e:
            execution_time = round(asyncio.get_event_loop().time() - start_time, 3)
            
            return ToolOutput(
                status=ToolStatus.ERROR,
                error=f"Workflow execution failed: {str(e)}",
                execution_time=execution_time
            )


# Register the workflow tools
register_tool(RepoCloneWorkflowTool())
