#!/usr/bin/env python3
"""
Workflow Visualizer Comprehensive Demo

Demonstrates all capabilities of the workflow visualization system.

Author: Assistant
"""

import sys
import tempfile
from pathlib import Path

# Add current directory to path
sys.path.append('.')

from workflow import WorkflowVisualizer, visualize_workflow_file


def print_header(title):
    """Print a formatted header"""
    print(f"\n{'='*60}")
    print(f"🎨 {title}")
    print(f"{'='*60}")


def print_section(title):
    """Print a formatted section"""
    print(f"\n📋 {title}")
    print("-" * 50)


def demonstrate_visualizer():
    """Comprehensive demonstration of the workflow visualizer"""
    
    print_header("WORKFLOW VISUALIZER DEMONSTRATION")
    
    # Available workflows
    workflows = [
        ("workflows/simple_demo.yaml", "demo_workflow_summary.json"),
        ("workflows/repo_clone_workflow.yaml", None)
    ]
    
    formats = ["ascii", "mermaid", "graphviz", "html"]
    
    for workflow_file, result_file in workflows:
        if not Path(workflow_file).exists():
            print(f"⚠️  Skipping {workflow_file} (not found)")
            continue
        
        workflow_name = Path(workflow_file).stem.replace("_", " ").title()
        
        print_header(f"VISUALIZING: {workflow_name}")
        
        # Load the visualizer
        visualizer = WorkflowVisualizer()
        visualizer.load_workflow(workflow_file)
        
        # Load execution result if available
        if result_file and Path(result_file).exists():
            import json
            with open(result_file, 'r') as f:
                result_data = json.load(f)
                if 'workflow_result' in result_data:
                    from workflow.models import WorkflowResult
                    result_dict = result_data['workflow_result']
                    result = WorkflowResult(**result_dict)
                    visualizer.load_execution_result(result)
            print(f"📊 Loaded execution results from: {result_file}")
        
        # Show workflow info
        config = visualizer.config
        print(f"\n📝 Workflow Information:")
        print(f"   • Name: {config.name}")
        print(f"   • Description: {config.description or 'N/A'}")
        print(f"   • Version: {config.version}")
        print(f"   • Steps: {len(config.steps)}")
        print(f"   • Max Concurrency: {config.settings.max_concurrency}")
        print(f"   • Timeout: {config.settings.timeout}s")
        
        if visualizer.result:
            result = visualizer.result
            print(f"\n📈 Execution Results:")
            print(f"   • Status: {result.status}")
            print(f"   • Total Time: {result.total_execution_time}s")
            print(f"   • Successful: {result.successful_steps}/{result.total_steps}")
            print(f"   • Failed: {result.failed_steps}")
            print(f"   • Skipped: {result.skipped_steps}")
        
        # Generate visualizations in all formats
        for fmt in formats:
            print_section(f"{fmt.upper()} Visualization")
            
            try:
                if fmt == "ascii":
                    content = visualizer.generate_ascii(include_status=bool(visualizer.result))
                elif fmt == "mermaid":
                    content = visualizer.generate_mermaid(include_status=bool(visualizer.result))
                elif fmt == "graphviz":
                    content = visualizer.generate_graphviz(include_status=bool(visualizer.result))
                elif fmt == "html":
                    content = visualizer.generate_html_report()
                    # For HTML, just show a snippet
                    lines = content.split('\n')
                    content = '\n'.join(lines[:20]) + '\n... (truncated, full HTML available)'
                
                # Show first part of content
                lines = content.split('\n')
                preview_lines = lines[:15] if fmt != "html" else lines[:10]
                print('\n'.join(preview_lines))
                
                if len(lines) > len(preview_lines):
                    print(f"... ({len(lines) - len(preview_lines)} more lines)")
                
                print(f"\n✅ {fmt.upper()} format generated successfully!")
                
            except Exception as e:
                print(f"❌ Error generating {fmt.upper()}: {e}")
        
        # Show dependency analysis
        print_section("Dependency Analysis")
        
        levels = visualizer._calculate_execution_levels()
        print(f"📊 Execution Levels: {len(levels)}")
        
        for level, steps in enumerate(levels):
            print(f"   Level {level + 1}: {', '.join(steps)}")
        
        # Show step details
        print_section("Step Details")
        
        for step in config.steps:
            print(f"🔧 {step.name}:")
            print(f"   • Type: {step.type}")
            print(f"   • Tool: {step.tool_name or 'N/A'}")
            print(f"   • Dependencies: {', '.join(step.depends_on) if step.depends_on else 'None'}")
            
            if visualizer.result:
                step_result = visualizer._get_step_result(step.name)
                if step_result:
                    status_icon = {
                        'success': '✅',
                        'error': '❌',
                        'skipped': '⏭️'
                    }.get(step_result.status, '❓')
                    
                    print(f"   • Status: {status_icon} {step_result.status}")
                    print(f"   • Execution Time: {step_result.execution_time}s")
                    
                    if step_result.error:
                        print(f"   • Error: {step_result.error}")
            
            print()
    
    # Demonstrate programmatic usage
    print_header("PROGRAMMATIC USAGE EXAMPLES")
    
    print_section("Creating Custom Visualizations")
    
    print("""
# Example 1: Basic workflow visualization
from workflow import WorkflowVisualizer

visualizer = WorkflowVisualizer()
visualizer.load_workflow("my_workflow.yaml")

# Generate different formats
ascii_view = visualizer.generate_ascii()
mermaid_diagram = visualizer.generate_mermaid()
html_report = visualizer.generate_html_report()

# Example 2: With execution results
from workflow.models import WorkflowResult
result = WorkflowResult(...)  # Load from execution
visualizer.load_execution_result(result)

# Generate with status information
mermaid_with_status = visualizer.generate_mermaid(include_status=True)

# Example 3: Convenience function
from workflow import visualize_workflow_file

html_content = visualize_workflow_file(
    config_file="workflow.yaml",
    output_format="html",
    output_file="report.html",
    result_file="execution_results.json"
)
    """)
    
    print_section("CLI Usage Examples")
    
    print("""
# Generate HTML report with execution status
python visualize_workflow.py workflow.yaml --format html --output report.html --result results.json

# Generate Mermaid diagram
python visualize_workflow.py workflow.yaml --format mermaid

# Generate ASCII view with status
python visualize_workflow.py workflow.yaml --format ascii --result results.json

# Generate all formats for demo
python visualize_workflow.py --demo
    """)
    
    # Final summary
    print_header("VISUALIZATION CAPABILITIES SUMMARY")
    
    print("🎯 Features Demonstrated:")
    print("   ✅ Multiple output formats (ASCII, Mermaid, Graphviz, HTML)")
    print("   ✅ Dependency graph visualization")
    print("   ✅ Execution status integration")
    print("   ✅ Step timing and performance data")
    print("   ✅ Error highlighting and reporting")
    print("   ✅ Execution level analysis")
    print("   ✅ Interactive HTML reports")
    print("   ✅ CLI and programmatic interfaces")
    print("   ✅ Workflow metadata display")
    print("   ✅ Color-coded status indicators")
    
    print("\n🚀 The workflow visualizer provides comprehensive insights into:")
    print("   • Workflow structure and dependencies")
    print("   • Execution flow and parallelization opportunities")
    print("   • Performance bottlenecks and timing")
    print("   • Error patterns and failure points")
    print("   • Overall workflow health and status")
    
    print(f"\n🎉 Workflow visualization system is ready for production use!")


if __name__ == "__main__":
    try:
        demonstrate_visualizer()
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
