# Workflow Event System Guide

The workflow execution engine now supports real-time event firing that allows you to listen to workflow progress and step completions as they happen.

## Overview

The event system fires events at key points during workflow execution:

- **workflow_started**: When workflow execution begins
- **step_completed**: After each step completes (success, error, or skipped)
- **workflow_completed**: When the entire workflow finishes

## Event Data Structure

Each event is a `WorkflowEvent` object containing:

```python
class WorkflowEvent:
    event_type: str                    # Type of event
    timestamp: datetime.datetime       # When the event occurred
    workflow_name: str                 # Name of the workflow
    step_name: Optional[str]           # Name of the step (for step events)
    step_result: Optional[StepResult]  # Complete step result (for step_completed)
    workflow_progress: Dict[str, Any]  # Current progress information
    metadata: Dict[str, Any]           # Additional context-specific data
```

### Progress Information

The `workflow_progress` dictionary contains:

- `total_steps`: Total number of steps in the workflow
- `completed_steps`: Number of steps completed so far
- `successful_steps`: Number of successful steps
- `failed_steps`: Number of failed steps
- `progress_percentage`: Completion percentage (0-100)
- `executed_step_names`: List of successfully executed step names
- `failed_step_names`: List of failed step names

## Usage

### Basic Setup

```python
from workflow.engine import WorkflowEngine
from workflow.models import WorkflowEvent

def my_event_callback(event: WorkflowEvent):
    """Handle workflow events"""
    if event.event_type == "step_completed":
        print(f"Step {event.step_name} completed: {event.step_result.status}")
        print(f"Progress: {event.workflow_progress['progress_percentage']}%")

# Create engine with event callbacks
engine = WorkflowEngine(event_callbacks=[my_event_callback])

# Execute workflow normally
result = await engine.execute(config)
```

### Multiple Callbacks

You can register multiple event callbacks:

```python
def step_logger(event: WorkflowEvent):
    """Log step completions"""
    if event.event_type == "step_completed":
        print(f"STEP: {event.step_name} -> {event.step_result.status}")

async def progress_tracker(event: WorkflowEvent):
    """Async progress tracking"""
    if event.event_type == "step_completed":
        progress = event.workflow_progress
        await send_progress_update(progress['progress_percentage'])

def workflow_monitor(event: WorkflowEvent):
    """Monitor workflow start/end"""
    if event.event_type == "workflow_started":
        print(f"Started: {event.workflow_name}")
    elif event.event_type == "workflow_completed":
        print(f"Completed: {event.metadata['overall_status']}")

engine = WorkflowEngine(event_callbacks=[
    step_logger,
    progress_tracker,  # Async callback
    workflow_monitor
])
```

### Event Filtering

Filter events by type in your callbacks:

```python
def error_handler(event: WorkflowEvent):
    """Handle only error events"""
    if (event.event_type == "step_completed" and 
        event.step_result.status == "error"):
        
        print(f"ERROR in {event.step_name}: {event.step_result.error}")
        # Send alert, log to file, etc.

def success_tracker(event: WorkflowEvent):
    """Track successful completions"""
    if (event.event_type == "step_completed" and 
        event.step_result.status == "success"):
        
        execution_time = event.step_result.execution_time
        print(f"✅ {event.step_name} completed in {execution_time}s")
```

## Real-World Examples

### Progress Bar

```python
def progress_bar_callback(event: WorkflowEvent):
    """Display a progress bar"""
    if event.event_type == "workflow_started":
        print(f"Starting {event.workflow_name}...")
        
    elif event.event_type == "step_completed":
        progress = event.workflow_progress
        percentage = progress['progress_percentage']
        completed = progress['completed_steps']
        total = progress['total_steps']
        
        # Simple progress bar
        bar_length = 30
        filled = int(bar_length * percentage / 100)
        bar = '█' * filled + '░' * (bar_length - filled)
        
        print(f"\r[{bar}] {percentage:5.1f}% ({completed}/{total})", end='')
        
    elif event.event_type == "workflow_completed":
        print(f"\n✅ Workflow completed: {event.metadata['overall_status']}")
```

### Logging to File

```python
import json
from datetime import datetime

def file_logger(event: WorkflowEvent):
    """Log all events to a file"""
    log_entry = {
        "timestamp": event.timestamp.isoformat(),
        "event_type": event.event_type,
        "workflow_name": event.workflow_name,
        "step_name": event.step_name,
        "metadata": event.metadata
    }
    
    if event.step_result:
        log_entry["step_result"] = {
            "status": event.step_result.status,
            "execution_time": event.step_result.execution_time,
            "error": event.step_result.error
        }
    
    with open("workflow_events.log", "a") as f:
        f.write(json.dumps(log_entry) + "\n")
```

### External System Integration

```python
import requests

async def webhook_notifier(event: WorkflowEvent):
    """Send events to external webhook"""
    if event.event_type in ["workflow_started", "workflow_completed"]:
        payload = {
            "event": event.event_type,
            "workflow": event.workflow_name,
            "timestamp": event.timestamp.isoformat(),
            "metadata": event.metadata
        }
        
        try:
            await requests.post(
                "https://your-webhook-url.com/workflow-events",
                json=payload,
                timeout=5
            )
        except Exception as e:
            print(f"Failed to send webhook: {e}")
```

### Dashboard Updates

```python
class WorkflowDashboard:
    def __init__(self):
        self.current_workflow = None
        self.step_statuses = {}
    
    def update_dashboard(self, event: WorkflowEvent):
        """Update dashboard with event data"""
        if event.event_type == "workflow_started":
            self.current_workflow = event.workflow_name
            self.step_statuses = {}
            self.render_workflow_start(event)
            
        elif event.event_type == "step_completed":
            self.step_statuses[event.step_name] = {
                "status": event.step_result.status,
                "time": event.step_result.execution_time,
                "error": event.step_result.error
            }
            self.render_step_update(event)
            
        elif event.event_type == "workflow_completed":
            self.render_workflow_complete(event)
    
    def render_workflow_start(self, event):
        print(f"🚀 Dashboard: Starting {event.workflow_name}")
    
    def render_step_update(self, event):
        status_emoji = {"success": "✅", "error": "❌", "skipped": "⏭️"}
        emoji = status_emoji.get(event.step_result.status, "❓")
        print(f"📊 Dashboard: {emoji} {event.step_name}")
    
    def render_workflow_complete(self, event):
        print(f"🏁 Dashboard: Workflow completed - {event.metadata['overall_status']}")

# Usage
dashboard = WorkflowDashboard()
engine = WorkflowEngine(event_callbacks=[dashboard.update_dashboard])
```

## Error Handling

Event callbacks should handle their own errors to avoid disrupting workflow execution:

```python
def safe_event_callback(event: WorkflowEvent):
    """Event callback with error handling"""
    try:
        # Your event handling logic here
        process_event(event)
    except Exception as e:
        # Log error but don't raise it
        print(f"Event callback error: {e}")
        # Optionally log to file, send alert, etc.
```

The workflow engine will catch callback errors and continue execution, but it's better to handle them explicitly.

## Performance Considerations

- Event callbacks are called synchronously during workflow execution
- Async callbacks are awaited, so they can slow down workflow execution
- Keep callbacks lightweight for better performance
- For heavy processing, consider queuing events and processing them separately

## Migration from Previous Versions

If you're upgrading from a version without events:

1. Existing code continues to work unchanged
2. Add event callbacks to `WorkflowEngine` constructor to enable events
3. No changes needed to workflow configurations
4. Events are fired automatically for all workflow executions

## Complete Example

```python
import asyncio
from workflow.engine import WorkflowEngine
from workflow.models import WorkflowEvent, WorkflowConfig, WorkflowStep

def comprehensive_event_handler(event: WorkflowEvent):
    """Handle all types of workflow events"""
    timestamp = event.timestamp.strftime("%H:%M:%S")
    
    if event.event_type == "workflow_started":
        print(f"[{timestamp}] 🚀 Started: {event.workflow_name}")
        
    elif event.event_type == "step_completed":
        status = event.step_result.status
        time_taken = event.step_result.execution_time
        progress = event.workflow_progress['progress_percentage']
        
        status_emoji = {"success": "✅", "error": "❌", "skipped": "⏭️"}[status]
        print(f"[{timestamp}] {status_emoji} {event.step_name} ({time_taken}s) - {progress}%")
        
        if event.step_result.error:
            print(f"           Error: {event.step_result.error}")
            
    elif event.event_type == "workflow_completed":
        status = event.metadata['overall_status']
        total_time = event.metadata['total_execution_time']
        print(f"[{timestamp}] 🏁 Completed: {status} in {total_time}s")

async def main():
    # Create engine with event monitoring
    engine = WorkflowEngine(event_callbacks=[comprehensive_event_handler])
    
    # Create and execute workflow
    config = WorkflowConfig(
        name="Example Workflow",
        steps=[
            WorkflowStep(name="step1", tool_name="some.tool", input_data={}),
            WorkflowStep(name="step2", tool_name="other.tool", input_data={}, depends_on=["step1"])
        ]
    )
    
    result = await engine.execute(config)
    print(f"Final result: {result.status}")

if __name__ == "__main__":
    asyncio.run(main())
```

This event system provides powerful real-time monitoring capabilities for workflow execution, enabling better observability, debugging, and integration with external systems.
