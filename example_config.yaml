# Example configuration for repository cloning workflow
# This file demonstrates how to configure multiple repository clones

repositories:
  - name: "copier-templates"
    url: "https://github.com/copier-org/copier-templates-extensions.git"
    destination: "./cloned-repos/copier-templates"
    type: "copier"
    data:
      project_name: "My Copier Project"
      author_name: "Demo User"
      description: "A project cloned from copier template"
    
  - name: "python-project"
    url: "https://github.com/copier-org/copier.git"
    destination: "./cloned-repos/python-project"
    type: "copier"
    checkout: "main"
    data:
      project_name: "Python Demo"
      author_name: "Demo User"
      email: "<EMAIL>"

# Workflow settings
workflow:
  max_concurrency: 2
  timeout: 300
  fail_fast: false
  cleanup_on_error: true
  
# Output settings
output:
  log_file: "./workflow.log"
  summary_file: "./workflow_summary.json"
