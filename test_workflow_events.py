#!/usr/bin/env python3
"""
Test Workflow Events

Simple test to verify the workflow event system is working correctly.

Author: Assistant
"""

import asyncio
from typing import List
from workflow.engine import WorkflowEngine
from workflow.models import WorkflowEvent, WorkflowConfig, WorkflowStep, WorkflowSettings


class EventCollector:
    """Collects events for testing"""
    
    def __init__(self):
        self.events: List[WorkflowEvent] = []
    
    def collect_event(self, event: WorkflowEvent):
        """Synchronous event collector"""
        self.events.append(event)
        print(f"📝 Collected event: {event.event_type} - {event.step_name or 'workflow'}")
    
    async def async_collect_event(self, event: WorkflowEvent):
        """Asynchronous event collector"""
        self.events.append(event)
        print(f"📝 Async collected event: {event.event_type} - {event.step_name or 'workflow'}")
    
    def get_events_by_type(self, event_type: str) -> List[WorkflowEvent]:
        """Get events of a specific type"""
        return [e for e in self.events if e.event_type == event_type]
    
    def print_summary(self):
        """Print summary of collected events"""
        print(f"\n📊 Event Collection Summary:")
        print(f"   Total events: {len(self.events)}")
        
        event_counts = {}
        for event in self.events:
            event_counts[event.event_type] = event_counts.get(event.event_type, 0) + 1
        
        for event_type, count in event_counts.items():
            print(f"   • {event_type}: {count}")


async def test_basic_workflow_events():
    """Test basic workflow event firing"""
    
    print("🧪 Testing Basic Workflow Events")
    print("-" * 40)
    
    # Create event collector
    collector = EventCollector()
    
    # Create workflow engine with both sync and async callbacks
    engine = WorkflowEngine(event_callbacks=[
        collector.collect_event,        # Sync callback
        collector.async_collect_event   # Async callback
    ])
    
    # Create simple workflow
    config = WorkflowConfig(
        name="Test Workflow",
        steps=[
            WorkflowStep(
                name="test_step_1",
                tool_name="file.info",
                input_data={"path": "."}
            ),
            WorkflowStep(
                name="test_step_2", 
                tool_name="file.list",
                input_data={"path": "."},
                depends_on=["test_step_1"]
            )
        ],
        settings=WorkflowSettings(max_concurrency=1)
    )
    
    print(f"✅ Created test workflow with {len(config.steps)} steps")
    
    # Execute workflow
    print("🚀 Executing workflow...")
    result = await engine.execute(config)
    
    print(f"✅ Workflow completed: {result.status}")
    
    # Analyze collected events
    collector.print_summary()
    
    # Verify expected events
    workflow_started = collector.get_events_by_type("workflow_started")
    step_completed = collector.get_events_by_type("step_completed")
    workflow_completed = collector.get_events_by_type("workflow_completed")
    
    print(f"\n🔍 Event Verification:")
    print(f"   • workflow_started events: {len(workflow_started)} (expected: 2 - sync + async)")
    print(f"   • step_completed events: {len(step_completed)} (expected: 4 - 2 steps × 2 callbacks)")
    print(f"   • workflow_completed events: {len(workflow_completed)} (expected: 2 - sync + async)")
    
    # Check event data
    if step_completed:
        first_step_event = step_completed[0]
        print(f"\n📋 Sample Step Event Data:")
        print(f"   • Step name: {first_step_event.step_name}")
        print(f"   • Step status: {first_step_event.step_result.status}")
        print(f"   • Execution time: {first_step_event.step_result.execution_time}s")
        print(f"   • Progress: {first_step_event.workflow_progress}")
    
    return len(collector.events) > 0


async def test_parallel_workflow_events():
    """Test events with parallel execution"""
    
    print("\n🧪 Testing Parallel Workflow Events")
    print("-" * 40)
    
    collector = EventCollector()
    engine = WorkflowEngine(event_callbacks=[collector.collect_event])
    
    # Create workflow with parallel steps
    config = WorkflowConfig(
        name="Parallel Test Workflow",
        steps=[
            WorkflowStep(name="init", tool_name="file.info", input_data={"path": "."}),
            WorkflowStep(name="parallel_a", tool_name="file.info", input_data={"path": "workflow"}, depends_on=["init"]),
            WorkflowStep(name="parallel_b", tool_name="file.info", input_data={"path": "tools"}, depends_on=["init"]),
            WorkflowStep(name="final", tool_name="file.list", input_data={"path": "."}, depends_on=["parallel_a", "parallel_b"])
        ],
        settings=WorkflowSettings(max_concurrency=2)
    )
    
    print(f"✅ Created parallel test workflow")
    print("   Structure: init → [parallel_a, parallel_b] → final")
    
    # Execute workflow
    print("🚀 Executing parallel workflow...")
    result = await engine.execute(config)
    
    print(f"✅ Parallel workflow completed: {result.status}")
    
    # Analyze events
    collector.print_summary()
    
    # Check for parallel execution metadata
    step_events = collector.get_events_by_type("step_completed")
    parallel_events = [e for e in step_events if e.metadata.get("parallel_execution")]
    
    print(f"\n🔍 Parallel Execution Analysis:")
    print(f"   • Total step events: {len(step_events)}")
    print(f"   • Parallel execution events: {len(parallel_events)}")
    
    return len(parallel_events) > 0


async def test_error_handling_events():
    """Test events when steps fail"""
    
    print("\n🧪 Testing Error Handling Events")
    print("-" * 40)
    
    collector = EventCollector()
    engine = WorkflowEngine(event_callbacks=[collector.collect_event])
    
    # Create workflow with a step that will fail
    config = WorkflowConfig(
        name="Error Test Workflow",
        steps=[
            WorkflowStep(
                name="good_step",
                tool_name="file.info",
                input_data={"path": "."}
            ),
            WorkflowStep(
                name="bad_step",
                tool_name="file.read",
                input_data={"path": "/nonexistent/file.txt"},  # This should fail
                depends_on=["good_step"]
            )
        ],
        settings=WorkflowSettings(fail_fast=False)  # Continue on error
    )
    
    print(f"✅ Created error test workflow")
    print("   Contains a step that should fail (reading nonexistent file)")
    
    # Execute workflow
    print("🚀 Executing workflow with expected failure...")
    result = await engine.execute(config)
    
    print(f"✅ Workflow completed: {result.status}")
    
    # Analyze events for errors
    collector.print_summary()
    
    step_events = collector.get_events_by_type("step_completed")
    error_events = [e for e in step_events if e.step_result.status == "error"]
    success_events = [e for e in step_events if e.step_result.status == "success"]
    
    print(f"\n🔍 Error Event Analysis:")
    print(f"   • Successful step events: {len(success_events)}")
    print(f"   • Error step events: {len(error_events)}")
    
    if error_events:
        error_event = error_events[0]
        print(f"   • Error step: {error_event.step_name}")
        print(f"   • Error message: {error_event.step_result.error}")
    
    return len(error_events) > 0


async def main():
    """Run all event system tests"""
    
    print("🧪 WORKFLOW EVENT SYSTEM TESTS")
    print("=" * 50)
    
    tests = [
        ("Basic Workflow Events", test_basic_workflow_events),
        ("Parallel Workflow Events", test_parallel_workflow_events),
        ("Error Handling Events", test_error_handling_events)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, "PASS" if result else "FAIL"))
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results.append((test_name, "ERROR"))
    
    # Print test results
    print("\n" + "=" * 50)
    print("🧪 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    for test_name, status in results:
        status_emoji = {"PASS": "✅", "FAIL": "❌", "ERROR": "💥"}[status]
        print(f"{status_emoji} {test_name}: {status}")
    
    passed = len([r for r in results if r[1] == "PASS"])
    total = len(results)
    
    print(f"\n📊 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The workflow event system is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")


if __name__ == "__main__":
    asyncio.run(main())
