#!/usr/bin/env python3
"""
Comprehensive Workflow Demonstration

This script demonstrates the complete workflow system:
1. Reading YAML configuration files
2. Executing multiple tools concurrently
3. Repository cloning with Copier
4. Error handling and reporting
5. Summary generation

Author: Assistant
"""

import sys
import tempfile
import time
from pathlib import Path

# Add the current directory to Python path
sys.path.append('.')

from tools import execute, get_all_tools, get_namespaces


def print_header(title):
    """Print a formatted header"""
    print(f"\n{'='*60}")
    print(f"🚀 {title}")
    print(f"{'='*60}")


def print_step(step_num, description):
    """Print a formatted step"""
    print(f"\n📋 Step {step_num}: {description}")
    print("-" * 50)


def demonstrate_workflow():
    """Demonstrate the complete workflow system"""
    
    print_header("COMPREHENSIVE WORKFLOW DEMONSTRATION")
    
    # Step 1: Show available tools
    print_step(1, "Tool System Overview")
    namespaces = get_namespaces()
    tools = get_all_tools()
    
    print(f"📊 System Status:")
    print(f"   • Total Namespaces: {len(namespaces)}")
    print(f"   • Total Tools: {len(tools)}")
    print(f"   • Namespaces: {', '.join(sorted(namespaces))}")
    
    # Step 2: Demonstrate dry run
    print_step(2, "YAML Configuration Validation (Dry Run)")
    
    start_time = time.time()
    dry_result = execute('workflow.repo_clone', {
        'config_file': 'simple_workflow_config.yaml',
        'dry_run': True
    })
    dry_time = time.time() - start_time
    
    print(f"✅ Dry run completed in {dry_time:.3f}s")
    print(f"📋 Configuration Summary:")
    print(f"   • Status: {dry_result.status}")
    print(f"   • Total operations: {dry_result.data.total_steps}")
    print(f"   • Summary: {dry_result.data.summary}")
    
    print(f"\n📝 Operations that would be executed:")
    for i, step in enumerate(dry_result.data.steps, 1):
        exec_data = step.data['would_execute']
        print(f"   {i}. Clone: {exec_data['template_url']}")
        print(f"      → Destination: {exec_data['destination']}")
        print(f"      → Template data: {exec_data['data']}")
    
    # Step 3: Execute actual workflow
    print_step(3, "Concurrent Workflow Execution")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"🗂️  Using temporary directory: {temp_dir}")
        
        start_time = time.time()
        workflow_result = execute('workflow.repo_clone', {
            'config_file': 'simple_workflow_config.yaml',
            'dry_run': False,
            'override_destination': f'{temp_dir}/cloned-repos'
        })
        execution_time = time.time() - start_time
        
        print(f"\n⚡ Workflow completed in {execution_time:.3f}s")
        print(f"📊 Execution Results:")
        print(f"   • Status: {workflow_result.status}")
        print(f"   • Total repositories: {workflow_result.data.total_steps}")
        print(f"   • Successful: {workflow_result.data.successful_steps}")
        print(f"   • Failed: {workflow_result.data.failed_steps}")
        print(f"   • Summary: {workflow_result.data.summary}")
        
        print(f"\n📋 Detailed Results:")
        for i, step in enumerate(workflow_result.data.steps, 1):
            status_icon = '✅' if step.status == 'success' else '❌'
            print(f"   {status_icon} Repository {i}: {step.step_name}")
            print(f"      • Status: {step.status}")
            print(f"      • Execution time: {step.execution_time}s")
            
            if step.error:
                print(f"      • Error: {step.error}")
            elif step.data and hasattr(step.data, 'files_created'):
                print(f"      • Files created: {len(step.data.files_created)}")
                if step.data.files_created:
                    print(f"      • Sample files: {', '.join(step.data.files_created[:3])}...")
        
        # Step 4: Verify cloned repositories
        print_step(4, "Repository Verification")
        
        cloned_dir = Path(temp_dir) / "cloned-repos"
        if cloned_dir.exists():
            subdirs = [d for d in cloned_dir.iterdir() if d.is_dir()]
            print(f"📁 Cloned repositories found: {len(subdirs)}")
            
            for repo_dir in subdirs:
                print(f"   📂 {repo_dir.name}:")
                
                # Count files in the repository
                file_count = len([f for f in repo_dir.rglob('*') if f.is_file()])
                print(f"      • Total files: {file_count}")
                
                # Check for copier answers file
                answers_file = repo_dir / ".copier-answers.yml"
                if answers_file.exists():
                    print(f"      • ✅ Copier answers file present")
                else:
                    print(f"      • ⚠️  No copier answers file")
        else:
            print("⚠️  No cloned repositories directory found")
    
    # Step 5: Summary file verification
    print_step(5, "Summary File Verification")
    
    summary_result = execute('file.read', {'path': './simple_workflow_summary.json'})
    if summary_result.status == 'success':
        print("✅ Workflow summary file created successfully!")
        
        import json
        summary_data = json.loads(summary_result.data.content)
        
        print(f"📄 Summary file contents:")
        print(f"   • Timestamp: {summary_data.get('timestamp', 'N/A')}")
        print(f"   • Config file: {summary_data['workflow_result']['config_file']}")
        print(f"   • Total execution time: {summary_data['workflow_result']['total_execution_time']}s")
        print(f"   • Success rate: {summary_data['workflow_result']['successful_steps']}/{summary_data['workflow_result']['total_steps']}")
    else:
        print("⚠️  Summary file not found or could not be read")
    
    # Final summary
    print_header("DEMONSTRATION COMPLETE")
    
    print("🎉 Successfully demonstrated:")
    print("   ✅ YAML configuration parsing and validation")
    print("   ✅ Dry-run capability for safe testing")
    print("   ✅ Concurrent execution of multiple tools")
    print("   ✅ Repository cloning with Copier templates")
    print("   ✅ Comprehensive error handling")
    print("   ✅ Detailed execution reporting")
    print("   ✅ Summary file generation")
    print("   ✅ Real-world workflow orchestration")
    
    print(f"\n📊 Final System Stats:")
    print(f"   • Total tools available: {len(tools)}")
    print(f"   • Namespaces: {len(namespaces)}")
    print(f"   • Workflow execution time: {execution_time:.3f}s")
    print(f"   • Repositories cloned: {workflow_result.data.successful_steps}")
    
    print(f"\n🚀 The tool system is ready for production use!")


if __name__ == "__main__":
    try:
        demonstrate_workflow()
    except Exception as e:
        print(f"\n❌ Demonstration failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
